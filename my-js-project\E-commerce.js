const ecommerceData = {
  users: [
    {
      id: 'u1',
      name: '<PERSON>',
      email: '<EMAIL>',
      age: 32,
      isPremium: true,
      joinDate: '2020-05-15',
      preferences: {
        categories: ['electronics', 'books'],
        notificationSettings: {
          email: true,
          sms: false
        }
      },
      addresses: [
        {
          type: 'home',
          street: '123 Main St',
          city: 'New York',
          primary: true
        },
        {
          type: 'work',
          street: '456 Business Ave',
          city: 'New York',
          primary: false
        }
      ]
    },
    // More users...
  ],
  products: [
    {
      id: 'p1',
      name: 'Ultra HD Smart TV',
      category: 'electronics',
      price: 899.99,
      stock: 45,
      specs: {
        brand: 'Sony',
        size: '55 inch',
        resolution: '4K'
      },
      tags: ['smart', '4k', 'android-tv'],
      reviews: [
        {
          userId: 'u1',
          rating: 5,
          comment: 'Excellent picture quality',
          date: '2023-01-10'
        },
        {
          userId: 'u2',
          rating: 4,
          comment: 'Great but expensive',
          date: '2023-01-15'
        }
      ]
    },
    // More products...
  ],
  orders: [
    {
      id: 'o1',
      userId: 'u1',
      date: '2023-02-01',
      status: 'delivered',
      items: [
        {
          productId: 'p1',
          quantity: 1,
          priceAtPurchase: 899.99,
          discountApplied: 0.1
        },
        {
          productId: 'p2',
          quantity: 2,
          priceAtPurchase: 59.99,
          discountApplied: 0
        }
      ],
      shipping: {
        addressType: 'home',
        cost: 9.99,
        carrier: 'UPS'
      },
      payment: {
        method: 'credit_card',
        transactionId: 'txn_12345'
      }
    },
    // More orders...
  ],
  promotions: [
    {
      id: 'promo1',
      name: 'Spring Sale',
      validFrom: '2023-03-01',
      validTo: '2023-03-31',
      applicableCategories: ['electronics', 'home'],
      discountType: 'percentage',
      discountValue: 15,
      maxDiscountAmount: 200
    },
    // More promotions...
  ]
};
let deliveredProducts = ecommerceData.orders.filter((order) => order.status === 'delivered').map((order) => order.items.map((item) => item.productId));
console.log(deliveredProducts);
// Get all premium users who ordered electronics with their total spend
let premiumUsers = ecommerceData.users.filter((user) => user.isPremium  && 
 ecommerceData.products.some((item) => item.category === 'electronics') 
  

);
let totalSpend = ecommerceData.orders.reduce((orderTotal, order) => {
  return orderTotal + order.items.reduce((itemTotal, item) => {
    return itemTotal + (item.priceAtPurchase * item.quantity * (1 - item.discountApplied));
  }, 0);
}, 0);
console.log(totalSpend);
