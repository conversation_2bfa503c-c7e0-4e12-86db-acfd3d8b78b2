console.log("Hello, JavaScript!");
const array = [1, 2, 3];
console.log(array.map(x => x * 2)); // [2, 4, 6]
const complexArray = [
  {
    id: 1,
    name: "<PERSON>",
    scores: [95, 88, null, { bonus: 5 }],
    details: { active: true, joined: new Date("2023-01-15") },
    tags: ["developer", "senior", undefined]
  },
  [10, 20, [30, 40, { nested: "value" }], null],
  "JavaScript",
  null,
  undefined,
  {
    id: 2,
    name: "<PERSON>",
    scores: [75, [80, 85], { bonus: 10 }],
    details: { active: false, joined: new Date("2024-06-10") },
    tags: ["analyst", "junior"]
  },
  42,
  [
    { item: "apple", price: 1.5 },
    { item: "banana", price: 0.75 },
    { item: "orange", price: null }
  ],
  "",
  NaN
];
// array[index]
console.log(complexArray[0]); // First object: { id: 1, name: "<PERSON>", ... }
// at(index) (ES2022)
console.log(complexArray.at(-1)); // Last element: NaN

// push
complexArray.push({ id: 3, name: "<PERSON>" });
console.log(complexArray.length); // 11
// pop
console.log(complexArray.pop()); // { id: 3, name: "Charlie" }
// unshift
complexArray.unshift("new");
console.log(complexArray[0]); // "new"
// shift
console.log(complexArray.shift()); // "new"
// splice
complexArray.splice(2, 1, "NewScript"); // Replace "JavaScript" with "NewScript"
// concat
const newArray = complexArray.concat([100, 200]);
console.log(newArray.slice(-2)); // [100, 200]

// forEach
complexArray.forEach(item => console.log(typeof item)); // Logs type of each element
// map
const names = complexArray.map(item => item?.name || null);
console.log(names); // ["Alice", null, null, null, null, "Bob", null, null, null]
// for...of
for (const item of complexArray) {
  console.log(item?.id || "No ID");
}
// for...in (indices)
for (const i in complexArray) {
  console.log(`Index: ${i}`);
}

// filter
const validItems = complexArray.filter(item => item !== null && item !== undefined);
console.log(validItems.length); // Excludes null and undefined
// reduce
const totalScores = complexArray.reduce((acc, curr) => {
  return acc + (curr?.scores?.[0] || 0);
}, 0);
console.log(totalScores); // 95 + 75 = 170
// reduceRight
const rightTotal = complexArray.reduceRight((acc, curr) => {
  return acc + (curr?.scores?.[0] || 0);
}, 0); // Same as reduce
// flat
const flattened = complexArray.flat(2);
console.log(flattened); // Flattens nested arrays
// flatMap
const flatMapped = complexArray.flatMap(item => item?.tags || []);
console.log(flatMapped); // ["developer", "senior", undefined, "analyst", "junior"]
// indexOf
console.log(complexArray.indexOf(42)); // 6
// lastIndexOf
console.log(complexArray.lastIndexOf(42)); // 6
// find
const firstActive = complexArray.find(item => item?.details?.active);
console.log(firstActive); // { id: 1, name: "Alice", ... }
// findIndex
console.log(complexArray.findIndex(item => item?.name === "Bob")); // 5
// findLast (ES2023)
//console.log(complexArray.findLast(item => item?.details)); // { id: 2, name: "Bob", ... }
// findLastIndex (ES2023)
//console.log(complexArray.findLastIndex(item => item?.details)); // 5
// includes
console.log(complexArray.includes("JavaScript")); // true